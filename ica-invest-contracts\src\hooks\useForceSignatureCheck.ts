import { useState } from 'react';
import { forceSignatureCheckService } from '@/services/force-signature-check.service';

interface IForceSignatureCheckResponse {
  contractId: string;
  status: string;
  message: string;
}

export function useForceSignatureCheck() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const forceCheck = async (contractId: string): Promise<IForceSignatureCheckResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await forceSignatureCheckService(contractId);
      
      if (result.error) {
        setError('Erro ao verificar status de assinatura');
        return null;
      }

      return result.data;
    } catch (err) {
      setError('Erro inesperado ao verificar status de assinatura');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    forceCheck,
    loading,
    error,
  };
}
