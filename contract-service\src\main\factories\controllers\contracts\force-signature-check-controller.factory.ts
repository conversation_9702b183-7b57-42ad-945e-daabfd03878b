import { makeProcessAuditSignatureUseCase } from '@/main/factories/usecases/process-audit-signature-usecase.factory'
import { makeProcessInvestorSignatureUseCase } from '@/main/factories/usecases/process-investor-signature-usecase.factory'
import { ForceSignatureCheckController } from '@/presentation/http/controllers/contracts/force-signature-check.controller'

export function makeForceSignatureCheckController() {
  const processInvestorSignatureUseCase = makeProcessInvestorSignatureUseCase()
  const processAuditSignatureUseCase = makeProcessAuditSignatureUseCase()
  
  return new ForceSignatureCheckController(
    processInvestorSignatureUseCase,
    processAuditSignatureUseCase
  )
}
