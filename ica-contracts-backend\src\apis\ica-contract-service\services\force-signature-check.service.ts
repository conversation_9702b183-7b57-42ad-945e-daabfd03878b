import axios, { AxiosError, AxiosResponse } from 'axios';
import { Injectable, HttpException, BadRequestException } from '@nestjs/common';

export interface IForceSignatureCheckResponse {
  contractId: string;
  status: string;
  message: string;
}

@Injectable()
export class ForceSignatureCheckService {
  async perform(contractId: string): Promise<IForceSignatureCheckResponse> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/${contractId}/force-signature-check`;

      console.log('🔍 ForceSignatureCheckService - URL:', url);
      console.log('🔍 ForceSignatureCheckService - Contract ID:', contractId);

      const response = await axios.post<
        {},
        AxiosResponse<IForceSignatureCheckResponse>
      >(url, {}, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('✅ ForceSignatureCheckService - Response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ ForceSignatureCheckService - Error:', error);
      
      if (error instanceof AxiosError) {
        if (error.response?.status > 399 && error.response?.status < 499) {
          throw new BadRequestException(error.response.data);
        }

        throw new HttpException(
          error.response?.data?.message || 'Error checking signature status',
          error.response?.status || 500,
        );
      }
      
      throw new HttpException(
        'Error checking signature status',
        500,
      );
    }
  }
}
