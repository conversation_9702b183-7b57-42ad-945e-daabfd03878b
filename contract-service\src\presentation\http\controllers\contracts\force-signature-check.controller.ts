import { ProcessAuditSignatureUseCase } from '@/application/usecases/process-audit-signature/process-audit-signature.usecase'
import { ProcessInvestorSignatureUseCase } from '@/application/usecases/process-investor-signature/process-investor-signature.usecase'
import { ContractStatus } from '@/domain/entities/contracts'
import { badRequest, ok, serverError } from '@/presentation/http/helpers'
import type { HttpRequest, HttpResponse } from '@/presentation/http/protocols'

export class ForceSignatureCheckController {
  constructor(
    private readonly processInvestorSignatureUseCase: ProcessInvestorSignatureUseCase,
    private readonly processAuditSignatureUseCase: ProcessAuditSignatureUseCase
  ) {}

  async handle(request: HttpRequest<{ contractId: string }>): Promise<HttpResponse> {
    try {
      const { contractId } = request.params

      if (!contractId) {
        return badRequest(new Error('Contract ID is required'))
      }

      // Primeiro, tenta processar como assinatura do investidor
      const investorResult = await this.processInvestorSignatureUseCase.execute({
        id: contractId,
        status: ContractStatus.AWAITING_INVESTOR_SIGNATURE
      })

      if (investorResult.isLeft()) {
        return badRequest(investorResult.value)
      }

      // Se o status mudou para AWAITING_DEPOSIT, tenta processar como auditoria
      if (investorResult.value === ContractStatus.AWAITING_DEPOSIT) {
        const auditResult = await this.processAuditSignatureUseCase.execute({
          id: contractId,
          status: ContractStatus.AWAITING_AUDIT_SIGNATURE
        })

        if (auditResult.isLeft()) {
          return badRequest(auditResult.value)
        }

        return ok({
          contractId,
          status: auditResult.value,
          message: 'Signature status checked and updated successfully'
        })
      }

      return ok({
        contractId,
        status: investorResult.value,
        message: 'Signature status checked and updated successfully'
      })
    } catch (error) {
      return serverError(error as Error)
    }
  }
}
