import api from "@/core/api";
import returnError from "@/functions/returnError";
import { AxiosResponse } from "axios";

interface IForceSignatureCheckResponse {
  contractId: string;
  status: string;
  message: string;
}

export async function forceSignatureCheckService(
  contractId: string
): Promise<{
  data: IForceSignatureCheckResponse | null;
  loading: boolean;
  error: boolean | null;
}> {
  let data: IForceSignatureCheckResponse | null = null;
  let error: boolean | null = null;
  let loading = true;

  try {
    const response: AxiosResponse<IForceSignatureCheckResponse> = await api.post(
      `/contract/${contractId}/force-signature-check`
    );
    data = response.data;
  } catch (err) {
    error = true;
    returnError(err, "Tivemos um problema ao verificar o status de assinatura.");
  } finally {
    loading = false;
  }

  return { data, loading, error };
}
