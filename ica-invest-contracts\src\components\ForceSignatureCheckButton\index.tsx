import React from 'react';
import { useForceSignatureCheck } from '@/hooks/useForceSignatureCheck';
import { useQueryClient } from '@tanstack/react-query';

interface ForceSignatureCheckButtonProps {
  contractId: string;
  onSuccess?: (status: string) => void;
  className?: string;
  children?: React.ReactNode;
}

export function ForceSignatureCheckButton({
  contractId,
  onSuccess,
  className = '',
  children = 'Verificar Assinatura'
}: ForceSignatureCheckButtonProps) {
  const { forceCheck, loading, error } = useForceSignatureCheck();
  const queryClient = useQueryClient();

  const handleClick = async () => {
    const result = await forceCheck(contractId);
    
    if (result) {
      // Invalidar queries relacionadas a contratos para forçar refetch
      queryClient.invalidateQueries({
        queryKey: ['contracts']
      });

      // Invalidar dashboard do broker
      queryClient.invalidateQueries({
        queryKey: ['broker-dashboard']
      });

      if (onSuccess) {
        onSuccess(result.status);
      }
    }
  };

  return (
    <div className="flex flex-col">
      <button
        onClick={handleClick}
        disabled={loading}
        className={`
          px-4 py-2 rounded-lg font-medium transition-colors
          ${loading 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-orange-500 hover:bg-orange-600 text-white'
          }
          ${className}
        `}
      >
        {loading ? 'Verificando...' : children}
      </button>
      
      {error && (
        <span className="text-red-500 text-sm mt-1">
          {error}
        </span>
      )}
    </div>
  );
}
